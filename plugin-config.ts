/**
 * 列表关闭插件配置文件
 * <AUTHOR>
 * @date 2025-08-01
 */

export interface PluginConfig {
    // 字段映射配置
    fields: {
        id: string;           // 主键字段名
        billNo: string;       // 单据号字段名
        status: string;       // 状态字段名
    };
    
    // 服务配置
    service: {
        name: string;         // 服务名称
        operation: string;    // 操作方法名
    };
    
    // 状态值配置
    statusValues: {
        closed: string;       // 关闭状态值
        closedDisplay: string; // 关闭状态显示值
    };
    
    // 消息配置
    messages: {
        noSelection: string;
        confirmClose: string;
        successMessage: string;
        errorMessage: string;
        loadingMessage: string;
        alreadyClosed: string;
        noValidIds: string;
    };
}

// 默认配置
export const defaultConfig: PluginConfig = {
    fields: {
        id: "id",
        billNo: "billno", 
        status: "status"
    },
    service: {
        name: "YourBillService",
        operation: "closeBills"
    },
    statusValues: {
        closed: "closed",
        closedDisplay: "已关闭"
    },
    messages: {
        noSelection: "请先选择要关闭的单据!",
        confirmClose: "确定要关闭选中的 {count} 条单据吗？",
        successMessage: "成功关闭 {count} 条单据!",
        errorMessage: "关闭单据失败: {error}",
        loadingMessage: "正在关闭单据，请稍候...",
        alreadyClosed: "单据 {billNo} 已经是关闭状态，无法重复关闭",
        noValidIds: "未找到有效的单据ID!"
    }
};

// 工具函数：格式化消息
export function formatMessage(template: string, params: { [key: string]: any }): string {
    let result = template;
    for (const key in params) {
        result = result.replace(new RegExp(`\\{${key}\\}`, 'g'), params[key]);
    }
    return result;
}
